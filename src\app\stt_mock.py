def transcribe_segmented(path, segments):
    zh_lines = ["大家好，我们开始吧。", "下面讨论 UI 更新。"]
    return [
        {
            "speaker": seg[2],
            "start": seg[0],
            "end":   seg[1],
            "text":  zh_lines[i % 2],
        }
        for i, seg in enumerate(segments)
    ]

def transcribe(wav_path: str) -> str:
    """
    Mock 版 Whisper：忽略输入路径，直接返回一段固定中文。
    调用者期望只拿到 ‘text’ 字符串。
    """
    # 你可以把下面的字符串改成任何想展示的内容
    return "大家好，这是模拟语音转写文本。"