"""
pipeline.py  ·  Meeting-Notes AI  v1.0  (WoZ-ready)

统一的【音频 → 文本 → 摘要/待办】流水线。
返回值完全符合 docs/api_spec.md。
兼容旧测试：允许多余的 lang 参数但会忽略。

环境变量
---------
MOCK_STT=1      使用 stt_mock.transcribe     (默认 1)
MOCK_DIAR=1     使用 diar_mock.diarize       (默认 1)

典型调用
--------
out = run_pipeline("sample.wav")
"""

from pathlib import Path
from typing import List, Dict, Any
import os

# ---------- 选择 Mock / 真实模块 ----------
if os.getenv("MOCK_STT", "1") == "1":
    from .stt_mock import transcribe
else:
    from .stt import transcribe

if os.getenv("MOCK_DIAR", "1") == "1":
    from .diar_mock import diarize
else:
    from .diar import diarize

from .summarize import summarize
from .translate import translate
from .todo import extract_actions
from .classify import classify_meeting


# ---------- 小工具 ----------
def _concat_cn(segments: List[Dict[str, Any]]) -> str:
    """把中文分段拼成完整逐字稿"""
    return "\n".join(s["text"] for s in segments)


def _ensure_float(v):
    try:
        return float(v)
    except Exception:
        return 0.0


def _add_en_to_segments(segments: List[Dict[str, Any]]) -> None:
    """给每段文本补充 text_en 字段（就地修改）"""
    for seg in segments:
        seg["text_en"] = translate(seg["text"], src="zh", tgt="en")


# ---------- 主入口 ----------
def run_pipeline(audio_path: str, *_, **__) -> Dict[str, Any]:
    """
    Parameters
    ----------
    audio_path : str
        录音文件路径
    *_ / **__  :
        占位，用于吸收旧版测试传进来的多余参数（如 src_lang, tgt_lang）

    Returns
    -------
    dict : 满足 docs/api_spec.md
    """
    audio_path = Path(audio_path)

    # 1) 说话人分离
    diar_segments = diarize(audio_path)
    # diar_segments 需要包含 speaker/start/end/wav 四个键

    # 2) 逐段语音转写（中文）
    text_segments: List[Dict[str, Any]] = []
    for seg in diar_segments:
    # Mock diarize 可能返回三元组 (start, end, speaker)
        if isinstance(seg, tuple):
            start, end, speaker = seg
            wav_stub = str(audio_path)          # 占位给 transcribe
        else:                                   # 真模型返回 dict
            start, end, speaker = seg["start"], seg["end"], seg["speaker"]
            wav_stub      = seg.get("wav", str(audio_path))

        zh = transcribe(wav_stub)

        text_segments.append({
            "speaker": speaker,
            "start": float(start),
            "end":   float(end),
            "text":  zh.strip()
        })


    # 3) 翻译到英文
    _add_en_to_segments(text_segments)

    # 4) 拼整段中文逐字稿
    full_cn = _concat_cn(text_segments)

    # 5) 摘要
    summary_cn = summarize(full_cn, style="paragraph")
    summary_en = translate(summary_cn, src="zh", tgt="en")

    # 6) 待办提取
    todos = extract_actions(summary_cn)

    # 7) 会议类型分类
    meeting_type = classify_meeting(summary_cn)  # dict[label] = score

    return {
        "source_text": full_cn,
        "segments": text_segments,
        "summary": summary_cn,
        "summary_en": summary_en,
        "todos": todos,
        "meeting_type": meeting_type,
    }
