import tempfile
import subprocess
import pathlib
import whisper

_model = whisper.load_model("base")  # 已缓存后会秒载

def _slice_audio(src: str, start: float, dur: float) -> str:
    """
    使用 FFmpeg 把 [start, start+dur] 秒切成临时 WAV，返回路径。
    """
    tmp = tempfile.NamedTemporaryFile(
        suffix=".wav", delete=False
    )  # 留给 whisper 读取
    tmp.close()
    cmd = [
        "ffmpeg",
        "-loglevel",
        "quiet",
        "-y",
        "-ss",
        str(start),
        "-t",
        str(dur),
        "-i",
        src,
        "-ac",
        "1",
        "-ar",
        "16000",
        "-c:a",
        "pcm_s16le",
        tmp.name,
    ]
    subprocess.run(cmd, check=True)
    return tmp.name


def transcribe_segmented(
    audio_path: str, segments
):  # segments = [(start, end, spk), ...]
    """
    Whisper API 2.x 不再支持 offset/duration，因此改为先切片再转写。
    """
    results = []
    for start, end, spk in segments:
        clip = _slice_audio(audio_path, start, end - start)
        out = _model.transcribe(
            clip,
            language="zh",  # 你的项目原始语音是中文
            fp16=False,
        )
        pathlib.Path(clip).unlink(missing_ok=True)  # 清理临时文件
        results.append(
            {
                "speaker": spk,
                "start": start,
                "end": end,
                "text": out["text"].strip(),
            }
        )
    return results

def transcribe(audio_path: str, language: str = "en") -> str:
    out = _model.transcribe(
        audio_path,
        language=language,
        task="translate" if language == "en" else "transcribe",   # ★新增
        fp16=False,
    )
    return out["text"].strip()
