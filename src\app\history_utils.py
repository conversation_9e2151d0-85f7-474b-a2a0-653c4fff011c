from pathlib import Path
from datetime import datetime

HISTORY_DIR = Path(__file__).resolve().parent.parent.parent / "history"
HISTORY_DIR.mkdir(parents=True, exist_ok=True)   # 保证目录存在

def save_history(summary: str) -> Path:
    ts = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
    path = HISTORY_DIR / f"{ts}.md"
    path.write_text(summary, encoding="utf-8")
    return path

def list_history():
    """返回 [(文件名显示, Path), ...]，按时间倒序"""
    items = sorted(HISTORY_DIR.glob("*.md"), reverse=True)
    return [(p.stem, str(p)) for p in items]

def load_history(path: str) -> str:
    """读出 Markdown 文本"""
    return Path(path).read_text(encoding="utf-8")