from icalendar import Calendar, Event
from datetime import datetime, timedelta
from dateutil.parser import parse as parse_dt
from pathlib import Path
import uuid, tempfile

def _safe_due(due_str):
    """
    将 '2025-08-01' / '8/1' / '' → datetime
    空字符串返回今天+7天，便于演示
    """
    if not due_str.strip():
        return datetime.now() + timedelta(days=7)
    try:
        return parse_dt(due_str)
    except Exception:
        # 无法解析时同样给 7 天后
        return datetime.now() + timedelta(days=7)

def export_ics(todos: list, *, meeting_title="会议待办"):
    """
    todos: list[dict] = {"action": str, "owner": str, "due": str}
    返回生成的 .ics 文件路径（str）
    """
    cal = Calendar()
    cal.add("prodid", "-//MeetingNotes-AI//")
    cal.add("version", "2.0")

    for t in todos:
        event = Event()
        event.add("uid", str(uuid.uuid4()) + "@meeting-notes-ai")
        event.add("summary", t["action"])
        owner = t.get("owner", "")
        if owner:
            event.add("description", f"Owner: {owner}")
        due_dt = _safe_due(t.get("due", ""))
        # 设定为全天事件
        event.add("dtstart", due_dt.date())
        event.add("dtend",   (due_dt + timedelta(days=1)).date())
        cal.add_component(event)

    # 保存到临时目录
    tmp_path = Path(tempfile.gettempdir()) / f"{uuid.uuid4().hex}.ics"
    with open(tmp_path, "wb") as f:
        f.write(cal.to_ical())

    return str(tmp_path)