"""
gpt_utils_mock.py — 可配置离线桩
读取 assets/mock_responses.csv，根据正则匹配返回预设文本。
"""

import csv, re, pathlib, random

CSV_PATH = pathlib.Path(__file__).parent.parent / "assets/mock_responses.csv"
_CACHE = []

def _load():
    global _CACHE
    if _CACHE:
        return
    with open(CSV_PATH, newline='', encoding="utf-8") as f:
        reader = csv.DictReader(f)
        for row in reader:
            # 预编译正则以加速
            row["pattern"] = re.compile(row["pattern"])
            _CACHE.append(row)

def gpt_chat(system: str, user: str, lang: str = "zh", **_) -> str:
    if not CSV_PATH.exists():                 # CSV 不在也能跑
        _CACHE.append({
            "pattern": re.compile(".*"),
            "reply_zh": "【模拟】默认回复",
            "reply_en": "Default mock reply"
        })
        return

    _load()
    for row in _CACHE:
        if row["pattern"].search(user):
            return row["reply_zh"] if lang == "zh" else row["reply_en"]

    # 没命中时返回一个通用占位符
    fallback = [
        "【模拟】好的，已处理。",            # zh
        "Sure, done (mock)."              # en
    ]
    return fallback[0] if lang == "zh" else fallback[1]
